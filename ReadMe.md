# Multi-Task Series　Torch　Pattern Library

Info: 2023-2025

1. 基于数据加载的各种方式，搭建一种基于numpy或panda的数据工厂模型；
2. 基于各种算法的微调应用, 搭建一种基于Torch框架的多任务模型插件库，
   包括：导入包(设置环境，设备，资源等)，定义超参数，定义模型，定义数据载入，定义优化器，损失，实例化模型等，训练验证模板，推理绘图等，导出onnx

#### 组织架构

* 配置工具|环境
  * [x] 测试python环境可用：                                                         Configs/Check_ENVs.py
  * [?] 终端中的富文本和美观的格式:                                                    Configs/console.py

  * [ ] yaml(替代argparse): 凡是配置模型、路径、参数等信息，一律采用yaml读取配置的方式 在脚本中
  * [?] 将一些重复使用的预处理，例如transforms.Compose()  写入到*.py 配置文件中呢
  * [x] 字典转换成可调用类, 支持python/json/yml文件的参数调用：                                 pylts/config/ConfigDict.py
  * [x] 多卡训练时选择GPU:                                                                   pylts/config/devices_selected.py
  * [x] 分布式训练初始化[多卡训练]:                                                           pylts/config/distributed_init.py[class DDP_Trainer()]
  * [x] 注册器模块: 允许通过字符串映射和实例化模块。                                             pylts/config/registry.py
  * [x] 回调机制 CallbackHooks                                                               pylts/config/callbacks.py
* 数据
  * [ ] 数据加载，划分数据集
    * [ ] 文件加速读取 Path().rglob
  * [x] 加速check图像标签的自动化流程[ubuntu18.04]                                            data_provider/check_Lab_speedup/LabelClassifyImg.py
  * [?] *.rec 的数据存储和加载节省空间，在 PyTorch 中读取 .rec文件替代lmdb
  * [x] 多线程/进程 并行工具包 多线程并行io操作,多进程CPU密集操作                                pylts/datasets/multiParallel_toolkit.py
  * [x] 图像数据可视化 存储                                                                  pylts/datasets/ImgVisualization.py
  * [ ] 数据增强，标准化预处理
  * [ ] 数据采集逻辑：                                                                       pylts/utils/methods/logics.py
* 模型
  * [ ] 基础sota模型搭建                                                                    model_zoo/:
    * [x] 分类模型 efficientNetV2
    * [x] 分割模型 cv2.grabCut
    * [ ] 权重初始化
    * [x] 预训练模型下载                                                                                 timm_download.py
  * [ ] 模型导出onnx, 推理(use pt/onnx/trt) tensorRT模型加载与推理                            models/model_tensorRT.py
  * [-] 量化感知训练(QAT)
    * [x] 量化环境部署                                                                      models/QATEnv_check.py
    * [?] 量化训练
  
* 损失函数与优化器
  * [x] loss(for分类模型)                                                           pylts/utils/loss/select_loss.py
  * [x] 优化器调用                                                                  pylts/utils/optimizer/select_optim.py
    * [x] 调度器选择 scheduler                                                       pylts/utils/optimizer/scheduler.py
  * [ ] 部分参数冻结                                                                             freeze_module.py
  * [ ] 断点续传                                                                                 resume.py
  * [ ] 分布式训练, 验证
  * [?] 量化训练流程      models/model_qat.py
  * [ ] 训练策略, 如梯度裁剪、冻结权重训练
  * [ ] 分类/检测模型 的评价指标:                                                             estimate/indicator.py
* 监控模型及可视化
  * [ ] TensorBoardX
  * [x] GPU显存监控：在资源空闲时主动占用[排队模式]————tools/GPU_Monitor.sh
* 工具箱：utils/statistic/
  * [x] 日志模块 Mod_logger.py
  * [x] 时间模块 Mod_timer.py
  * [x] 调试模块 Mod_debug.py   包含模块：TryExcept ImageSaver
  * [x] 缓存模块 Np_cache.py    临时文件缓存 替换[np,np...]在内存中导致的溢出  

其他相关内容待更新：

1. 计算指标的基础配置 pylts/utils/metric/baseComputes.py
2. main.py中尝试加入debug模式，在opt.debug默认=False
3. 差1个主函数，加入以上所有可以默认载入的模块，如debug模块可默认关闭载入

#### 数据质量管理平台

做1个数据质量管理平台，通过在平台上操作各个设定好的模块，实现数据的高效筛选和管理；
数据主要包括: 图片、视频、pkl文件等(例如对于1个图片数据集，主要数据就是jpg或png图片)
平台前端: 以本地网页端的形式展示，可以执行一定的操作(例如点击等键盘鼠标操作)
后端功能包括：【后端是我本地电脑的ubuntu系统】

1. 能够使用指定的python脚本，执行相关操作：
例如: 前端点击可以新增1个子模块，在子模块中点击或输入：点击则弹窗中找到我提供的python脚本路径(或能直接粘贴输入脚本的绝对路径)，
假设加载的这个python脚本假设是1个随机抽取jpg图片的脚本，前端的子模块将需要提供输入数据路径、输出数据路径，需要抽取的图片数量
(在终端中执行的效果是 python my_random_pic.py --input "./xxx/" --output "./xxx/out" --img_num 10)
然后需要1个可以点击的执行按钮，点击后子模块在终端指定的虚拟环1境(conda activate env1)中执行并能看到打印的内容
执行完成后，能够提供1个按钮(output)，便于我一键打开
总的来说，对于这样一个能够执行python脚本的模块，只要该脚本提供多少个路径输入选项(在上面例子中是 input和output)，就需要在前端显示多少个路径按钮，便于输入和打开；
2. 人工筛选数据的子模块：

### 基础库说明

* 核心库:
  * torch (PyTorch): 核心的深度学习框架。
  * numpy: 用于进行数值计算。
  * cv2 (OpenCV): 用于图像和视频处理。
  * PIL (Pillow): 用于图像操作。

  * 机器学习与深度学习:
    * timm (PyTorch Image Models): 用于访问各种预训练的图像模型。
    * torchvision: 作为PyTorch生态的一部分，提供计算机视觉相关的工具和数据集。
    * tensorrt: 用于优化NVIDIA GPU上的深度学习模型。
    * pycuda: 用于在Python中进行CUDA编程，与GPU交互。
    * onnx: 用于实现不同深度学习框架之间的模型转换和互操作。
    * onnxruntime: 用于高效运行ONNX格式的模型。
    * nncf (Neural Network Compression Framework): 用于神经网络的压缩和量化。
    * openvino: 用于在英特尔硬件上优化和部署深度学习模型。
    * sklearn (scikit-learn): 提供常用的机器学习工具。
    * transformers: 用于自然语言处理任务（可能用于特定的模型）。
    * kornia: 一个可微分的计算机视觉库。

  * 数据与配置:
    * yaml (PyYAML): 用于读取和解析YAML格式的配置文件。
    * pandas: 用于数据处理和分析，尤其擅长处理Excel文件。
    * json: 用于处理JSON格式的数据。
    * easydict: 允许像访问对象属性一样访问字典的键。

  * 工具及其他:
    * tqdm: 用于在循环中显示进度条。
    * matplotlib: 用于数据可视化和绘图。
    * ffmpeg: 用于处理视频文件。
    * pynput: 用于监控和控制键盘输入。
    * rich: 用于在终端中创建富文本和精美的格式化输出。
    * colorlog: 用于输出带有颜色的日志信息。
